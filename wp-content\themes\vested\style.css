@charset "UTF-8"; /*
Theme Name:Vested Wordpress Theme
Author:<PERSON><PERSON>
Author URI:https://www.linkedin.com/in/haseeb-ur-rehman-wordpress-expert/
Description:Custom wordpress theme for Vested.
Version:1
*/


@import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800; 1,300..800&family=Poppins:ital,wght@0,100; 0,200; 0,300; 0,400; 0,500; 0,600; 0,700; 0,800; 0,900; 1,100; 1,200; 1,300; 1,400; 1,500; 1,600; 1,700; 1,800; 1,900&display=swap'); :root{--primary:#59595b;}
html,body{overflow-x:hidden; width:100%;}
body{font-family:'Open Sans',sans-serif;}
#mainWebContainer{overflow-x:hidden; position:relative;}
/************************* fonts,buttons,icons and text blocks styles**********************************/

h1{font-size:50px; color:#000000; line-height:50px; font-family:'Poppins',sans-serif;}
h2{font-size:44px; color:#000000; line-height:44px; font-family:'Poppins',sans-serif;}
h3{font-size:24px; color:#000000; line-height:26px;}
h4{font-size:20px; color:#000000; line-height:30px;}
h5{font-size:16px; color:#000000; line-height:24px;}
h6{font-size:14px; color:#000000; line-height:18px;}
.heading-inline{display:inline !important;}
a{color:#000000; font-weight:400; text-decoration:none; -webkit-transition:0.3s ease-in-out !important; -moz-transition:0.3s ease-in-out !important; -ms-transition:0.3s ease-in-out !important; -o-transition:0.3s ease-in-out !important; transition:0.3s ease-in-out !important;}
a:hover{color:#000000; text-decoration:none;}
a:focus{text-decoration:none; outline:none}
ul{margin:0; padding:0}
ul li{list-style:none;}
img{image-rendering:-webkit-optimize-contrast;}
#map{height:400px;}
/*--------------------------------------------------------------
# Top Bar
--------------------------------------------------------------*/

#topbar{background:#306178; height:40px; font-size:14px; border-bottom:1px solid #b49e3f; transition:all 0.5s;}
#topbar.topbar-scrolled{top:-40px;}
#topbar .contact-info a{color:#ffffff; font-size:14px; line-height:14px; font-weight:500;}
#topbar .contact-info a:hover{color:#ffffff;}
#topbar .contact-info a i{color:#ffffff;}
.cusBtn{position:relative; padding:10px 30px; font-weight:700; display:inline-block; text-transform:uppercase;}
.cusBtn1{border:1px solid #000; background:#000; color:#ffffff;}
.cusBtn1:hover{border:1px solid #ffffff; background:transparent; color:#ffffff;}
.cusBtn2{border:1px solid #ffffff; background:transparent; color:#ffffff;}
.cusBtn2:hover{border:1px solid  #000; background:#000; color:#ffffff;}
.cusBtn3{border:1px solid var(--primary); background:var(--primary); color:#ffffff;}
.cusBtn3:hover{border:1px solid  var(--primary); background:transparent; color:var(--primary);}
.cusBtn4{border:1px solid #000000; background:#000000; color:#ffffff; display:block; text-align:center;}
.cusBtn4:hover{border:1px solid  #ffffff; background:transparent; color:#ffffff;}
.pageMargin{margin-top:120px;}
/*--------------------------------------------------------------
# Header
--------------------------------------------------------------*/

#header{background-color:#ffffff; transition:all 0.5s; padding-block:22px;}
#header.header-scrolled{background:var(--primary); top:0;}
#header .logo img{width:200px;}
#header.header-scrolled .navbar li a,.navbar li a:focus{color:#ffffff;}
#header .navbar li.current-menu-item a:after,.navbar li a:hover:after{background:var(--primary);}
#header.header-scrolled .navbar li.current-menu-item a:after{background:#ffffff;}
#header.header-scrolled .logo img{filter:invert(100%);}
#header.header-scrolled .navbar li a:hover:after{background:#ffffff;}
/*--------------------------------------------------------------
# Navigation Menu
--------------------------------------------------------------*/

/**
* Desktop Navigation
*/


/*--------------------------------------------------------------
# Desktop Navigation
--------------------------------------------------------------*/

@media (min-width:1024px){
  .navbar{padding:0;}
  .navbar ul{margin:0; padding:0; display:flex; list-style:none; align-items:center; gap:20px;}
  .navbar li{position:relative; padding:0 15px 0 15px;}
  .navbar li:last-child{padding-right:30px;}
  .navbar li a,.navbar li a:focus{position:relative; display:flex; align-items:center; justify-content:space-between; color:#000000; white-space:nowrap; transition:0.3s; letter-spacing:1px; text-transform:uppercase; padding-bottom:10px; font-size:17px; font-weight:600; font-family:'Poppins',sans-serif;}
  .navbar li a:hover,.navbar li.current-menu-item a,.navbar li.current-menu-item:focus a,.navbar li:hover a{color:var(--primary);}
  .navbar li a:after{position:absolute; content:''; width:0; height:4px; bottom:-3px; left:50%; transform:translateX(-50%); background:var(--primary); -webkit-transition:width 0.3s ease; -moz-transition:width 0.3s ease; -ms-transition:width 0.3s ease; -o-transition:width 0.3s ease; transition:width 0.3s ease;}
  .navbar li.current-menu-item a:after,.navbar li a:hover:after{width:100%;}
  .navbar li.menu-item-has-children a:after{content:"\f078"; display:inline-block; font-family:'fontawesome'; position:absolute; top:50%; right:0; transform:translateY(-50%); font-size:12px;}
  .navbar li.menu-item-has-children ul.sub-menu{display:block; position:absolute; left:28px; top:calc(100% + 30px); margin:0; padding:10px 0; z-index:99; opacity:0; visibility:hidden; background:#fff; box-shadow:0 0 30px rgba(127,137,161,0.25); transition:0.3s; border-radius:4px;}
  .navbar li.menu-item-has-children ul.sub-menu li{min-width:200px;}
  .navbar li.menu-item-has-children ul.sub-menu a{padding:10px 20px; font-size:15px; text-transform:none; font-weight:600; color:#082744;}
  .navbar li.menu-item-has-children ul.sub-menu a i{font-size:12px;}
  .navbar li.menu-item-has-children ul.sub-menu a:hover,.navbar li.menu-item-has-children ul.sub-menu li:hover>a{color:#000000;}
}
@media (min-width:1024px) and (max-width:1366px){
  .navbar .menu-item-has-children .menu-item-has-children ul.sub-menu{left:-90%;}
  .navbar .menu-item-has-children .menu-item-has-children:hover>ul.sub-menu{left:-100%;}
}
@media (min-width:1024px){
  .mobile-nav-show,.mobile-nav-hide{display:none;}
}
/*--------------------------------------------------------------
# Mobile Navigation
--------------------------------------------------------------*/

@media (max-width:1023px){
  #header{padding:15px 0;}
  #header .logo img{width:200px;}
  .navbar{position:fixed; top:0; right:-100%; width:100%; max-width:400px; bottom:0; transition:0.3s; z-index:9997;}
  .navbar ul{position:absolute; inset:0; padding:50px 0 10px 0; margin:0; background:#000; opacity:.95; overflow-y:auto; transition:0.3s; z-index:9998;}
  .navbar a,.navbar a:focus{display:flex; align-items:center; justify-content:space-between; padding:10px 20px; font-size:15px; font-weight:600; color:rgba(255,255,255,0.7); white-space:nowrap; transition:0.3s;}
  .navbar a i,.navbar a:focus i{font-size:12px; line-height:0; margin-left:5px;}
  .navbar a:hover,.navbar .current-menu-item a,.navbar .current-menu-item:focus a,.navbar li:hover>a{color:#fff;}
  .navbar .menu-item-has-children ul.sub-menu,.navbar .menu-item-has-children .menu-item-has-children ul.sub-menu{position:static; display:none; padding:10px 0; margin:10px 20px; background-color:rgba(20,35,51,0.6);}
  .navbar .menu-item-has-children>.submenu-active,.navbar .menu-item-has-children .menu-item-has-children>.submenu-active{display:block;}
  .mobile-nav-show{font-size:28px; cursor:pointer; line-height:0; transition:0.5s; color:#000; padding-right:30px;}
  .mobile-nav-hide{color:rgba(255,255,255,0.9); font-size:28px; cursor:pointer; line-height:0; transition:0.5s; position:fixed; right:15px; top:25px; z-index:9999;}
  .mobile-nav-active{overflow:hidden;}
  .mobile-nav-active .navbar{right:0;}
  .mobile-nav-active .navbar:before{content:""; position:fixed; inset:0; background:#000000; opacity:.7; z-index:9996;}
}
@media (min-width:1023px){
  .mobile-nav-show,.mobile-nav-hide{display:none !important;}
}
.sub-menu-toggle{display:none !important;}
.id-scrool-fix{position:relative; top:-100px;}

/*--------------------------------------------------------------
# Home Additional Section 1
--------------------------------------------------------------*/

.homeAddSec1{position:relative;}
.homeAddSec1:after{content:""; position:absolute; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,.6); display:block;}
.homeAddSec1Inner{height:calc(100vh - 120px); min-height:575px; overflow:hidden; position:relative;}
.homeAddSec1Inner video{position:absolute; top:0; left:0; width:100%; height:100%; z-index:-1; object-fit:cover;}
.homeAddSec1 .homeAddSec1InnerText{top:50%; left:50%; transform:translate(-50%,-50%); width:1140px; max-width:100%; z-index:1; position:absolute; text-align:center;}
.homeAddSec1 .homeAddSec1InnerText h1{color:#ffffff; font-weight:700; font-size:46px; line-height:1;}
.homeAddSec1 .homeAddSec1InnerText p{color:#ffffff; font-size:20px; line-height:1.25;}
.homeAddSec1 a{margin-top:30px;}
/*--------------------------------------------------------------
# Home Additional Section 1
--------------------------------------------------------------*/

/*--------------------------------------------------------------
# About Us
--------------------------------------------------------------*/

.homeAddSec3 .homeAddSec3Left img{width:100%;}
.homeAddSec3Right h2{font-weight:700; font-size:40px; line-height:44px;}
.homeAddSec3Right p{font-size:18px; line-height:22px; margin-bottom:30px;}
/*--------------------------------------------------------------
# About Us
--------------------------------------------------------------*/

/*--------------------------------------------------------------
# Why Work With Us
--------------------------------------------------------------*/


 .why-work-with-us{position:relative; padding:120px 0; z-index:1;}
.why-work-with-us::before{content:""; position:absolute; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,.65); z-index:-1;display: block;}
.why-work-with-us .section-title{font-weight:700; font-size:40px; line-height:44px; position:relative; padding-bottom:30px; display:inline-block; margin-bottom:60px; color:#fff; text-align:center;}
.why-work-with-us .section-title:before{content:''; position:absolute; bottom:0; left:50%; transform:translate(-50%,-50%); width:33%; height:5px; background:#fff;}
.why-work-with-us .cards{display:grid; grid-template-columns:repeat(3,1fr); gap:20px;}
.why-work-with-us .card{background:#f5f5f5; color:#000; padding:25px 15px; border-radius:5px; text-align:center; box-shadow:0 4px 12px rgba(0,0,0,0.1); transition:transform 0.3s ease,box-shadow 0.3s ease; position:relative; overflow:hidden;}
.why-work-with-us .card::before{content:""; position:absolute; bottom:0; left:0; width:100%; height:5px; background-color:var(--primary); transition:0.3s; transform:scaleX(0); transform-origin:left;}
.why-work-with-us .card:hover::before{transform:scaleX(1);}
.why-work-with-us .card:hover{transform:translateY(-7px); box-shadow:0 12px 24px rgba(0,0,0,0.15);}
.why-work-with-us .card .icon{font-size:35px; color:var(--primary); margin-bottom:20px;}
.why-work-with-us .card h3{font-size:20px; margin-bottom:10px; font-weight:700;}
.why-work-with-us .card p{font-size:15px; color:#333;}
.why-work-with-us .section-footer{font-size:16px; color:#eee;}
/*--------------------------------------------------------------
#  Why Work With Us
--------------------------------------------------------------*/

/*--------------------------------------------------------------
# Properties
--------------------------------------------------------------*/
.homeAddSec6{position:relative; overflow:hidden;}
.homeAddSec6 .homeAddSec6Head{text-align:center;}
.homeAddSec6 .homeAddSec6Head h2{font-weight:700; font-size:40px; line-height:44px; position:relative; padding-bottom:30px; display:inline-block; margin-bottom:20px;}
.homeAddSec6 .homeAddSec6Head h2:before{content:''; position:absolute; bottom:0; left:50%; transform:translate(-50%,-50%); width:33%; height:5px; background:#000;}
.homeAddSec6 .homeAddSec6Col img{width:100%;}
.homeAddSec6 .homeAddSec6Col h4{font-size:16px; line-height:18px; font-weight:800; margin:10px 0 0 0;}
.homeAddSec6 .homeAddSec6Col p{font-size:14px; line-height:23px; color:var(--primary); margin:0;}
.homeAddSec6Slide.owl-carousel .owl-nav button.owl-next,.homeAddSec6Slide.owl-carousel .owl-nav button.owl-prev{color:#ffffff; outline:0; background-color:var(--primary);}
.homeAddSec6Slide .owl-nav [class*="owl-"]{color:#ffffff; font-size:25px; margin:0; padding:0; background:transparent; width:60px; height:30px; line-height:51px; position:absolute; left:-50px; -webkit-transition:0.5s; transition:0.5s; top:50%; -webkit-transform:translateY(-50%); transform:translateY(-50%); opacity:0; visibility:hidden; border-radius:20px; border-style:solid}
.homeAddSec6Slide .owl-nav [class*="owl-"]{left:-25px; opacity:1; visibility:hidden;}
.homeAddSec6Slide .owl-nav [class*="owl-"].owl-next{left:auto; right:-25px;}
.homeAddSec6Slide:hover .owl-nav [class*="owl-"]{visibility:visible;}
.homeAddSec6 .cusBtn1{margin-top:30px;}
.homeAddSec6 .cusBtn1:hover{background:#000; color:#fff;}
/*--------------------------------------------------------------
# Properties
--------------------------------------------------------------*/


/*--------------------------------------------------------------
# Contact
--------------------------------------------------------------*/

.contactSec{position:relative; padding:120px 0;}
.contactSec:before{content:''; position:absolute; top:0; left:0; background:rgb(0 0 0 / 65%); width:100%; height:100%; z-index:0; display:block;}
.contactSecLeft,.contactSecRight{position:relative; z-index:1;}
.contactSecRight h2{margin:0 0 30px 0; font-size:40px; line-height:44px; font-weight:700; color:#ffffff;}
.contactSecRight p{color:#ffffff;}
.homeAddSec5RightCol{position:relative; padding-left:50px;}
.contact-box{transition:0.5s; padding:15px; position:relative; margin-bottom:15px; display:flex; align-items:center; gap:25px; background:#f5f5f5;}
.contact-box .icon{width:70px; height:70px; text-align:center; line-height:60px; border:2px solid #000; color:#000; transition:0.5s; display:flex; align-items:center; justify-content:center;}
.contact-box .icon i::before{font-size:22px;}
.contact-box h4{font-size:18px; line-height:18px; margin:0 0 10px 0; font-weight:600; color:#000;}
.contact-box:hover h4{color:#ffffff;}
.contact-box .content p{margin-bottom:0; color:#000; font-size:16px; line-height:18px; font-weight:600;}
.contact-box:hover p{color:#ffffff;}
.contact-box .content p a{color:#000; font-weight:600;}
.contact-box:hover .content p a,.contact-box:hover .content p a:focus{color:#ffffff;}
.contact-box:hover,.contact-box:focus{transform:translateY(-10px); background:var(--primary);}
.contact-box:hover .icon,.contact-box:focus .icon{border:2px solid #ffffff; color:#ffffff;}
/*--------------------------------------------------------------
# Contact
--------------------------------------------------------------*/

.mainBanner{position:relative;}
.mainBanner:before{content:""; position:absolute; top:0; left:0; width:100%; height:100%; display:block; background:rgba(0,0,0,0.5);}
.mainBannerText{padding:250px 0 50px 0; position:relative; z-index:1;}
.mainBannerText h2{text-align: center;color:#ffffff; font-size:38px; line-height:38px; text-transform:uppercase;}
.breadcrumb{margin:0; display:flex; align-items:center; justify-content: center;}
.breadcrumb a{color:#ffffff; text-transform:uppercase; font-size:13px; line-height:16px;}
.breadcrumb span{color:#ffffff; text-transform:uppercase; font-size:13px; line-height:16px;}
.breadcrumb span i{font-size:13px; line-height:15px;}
.brd-bottom{border-bottom: 2px solid var(--brownColor);}


/*--------------------------------------------------------------
# About
--------------------------------------------------------------*/


/*--------------------------------------------------------------
# About
--------------------------------------------------------------*/
.aboutAddSec1 .aboutAddSec1Right img{width:100%;}
.aboutAddSec1Right, .aboutAddSec1Right div {height: 100%;}
.aboutAddSec1Right div img {object-fit: cover; object-position: center; height: 100%; width: 100%;}
.aboutAddSec1Left h2{font-weight:900; font-size:30px; line-height:40px; color:var(--primary);}
.aboutAddSec1Left h3{
	font-size: 18px;

    font-weight: 800;
}
.aboutAddSec1Left p{font-size:18px; line-height:22px;}
.aboutAddSec1LeftInner{background:#ffffff; margin-left:-150px; padding:40px; margin-top:30px; border:1px solid #eeeeee; box-shadow:5px 10px 10px 0 rgba(0,0,0,.3); position:relative; z-index:1;}
.aboutAddSec1LeftInner h3{font-weight:700; font-size:20px; line-height:30px; color:var(--primary);}
.aboutAddSec1LeftInner p{font-size:18px; line-height:22px; text-align:justify;}
.aboutAddSec1Bottom{display:flex; flex-wrap:wrap; gap:50px;  justify-content:space-between;}
.aboutAddSec1BottomStats{text-align:center;}
.aboutAddSec1BottomStats h2{margin:0; color:var(--primary); font-weight:800; font-size:40px; line-height:40px; text-transform:uppercase;}
.aboutAddSec1BottomStats p{font-size:18px; font-family:"Cinzel",serif; font-weight:800;}



.aboutAddSec1Left button.accordion-button{font-size:18px; padding:12px 0; background:transparent;}
.aboutAddSec1Left .accordion-button::after{position:absolute; content:"\f068"; font-family:"Font Awesome 6 Free"; font-weight:900; line-height:25px; border-radius:50%; right:16px; font-size:18px; line-height:18px; color:var(--primary); text-align:center; z-index:1; background-image:none; transform:none;}
.aboutAddSec1Left .collapsed::after{content:"\2b" !important;}
.aboutAddSec1Left .accordion-body{padding-inline:0; padding-bottom:14px; padding-top:0;}
.aboutAddSec1Left .accordion-body ul li{list-style:disc; list-style-position:inside;}
.aboutAddSec1Left  .accordion-body ul li::marker{color:var(--primary);}
.aboutAddSec1Left .accordion-button:not(.collapsed),.accordion-button .collapsed{color:var(--primary); background-color:transparent; box-shadow:none;}
.aboutAddSec1Left .accordion-item{color:#000000; background-color:transparent; border-bottom:1px solid var(--primary);}
.aboutAddSec1Left .accordion-item{background-color:transparent; border-bottom:1px solid #4d4d4d;}
.aboutAddSec1Left .accordion-item:nth-last-of-type(1){border-bottom:none !important;}
.aboutAddSec1Left .accordion-button:focus{z-index:3; border-color:transparent; outline:0; box-shadow:none;}



.team-block,.team-block .inner-box{position:relative;}
.team-block .image-box{position:relative;}
.team-block .image-box .image{position:relative; overflow:hidden; margin-bottom:0; z-index:1; border:2px solid #000000; padding:5px;}
.team-block .image-box .image img{width:100%; -webkit-transition:all .4s ease; transition:all .4s ease; filter:grayscale(1);}
.team-block .info-box{position:relative; padding:19px 70px 19px 30px; background-color:#000000; max-width:322px; margin:-35px auto 0; -webkit-box-shadow:0 10px 60px rgba(0,0,0,.07); box-shadow:0 10px 60px #00000012; z-index:2; min-height:112px;}
.team-block .info-box:before{content:""; position:absolute; left:0; top:0; height:100%; width:100%; width:0; background-color:var(--primary); -webkit-transition:all .3s ease; transition:all .3s ease;}
.team-block .info-box:after{content:""; position:absolute; left:0; height:100%; width:100%; top:50%; width:9px; height:45px; background-color:var(--primary); -webkit-transform:translateY(-50%); transform:translateY(-50%); -webkit-transition:all .3s ease; transition:all .3sease;}
.team-block .info-box h4{position:relative; font-size:18px; font-family:"Cinzel",serif; font-weight:800; color:#ffffff;}
.team-block .info-box .designation{position:relative; color:#999999; font-size:16px; line-height:18px; font-weight:700; display:block; -webkit-transition:all .3sease; transition:all .3sease;}
.team-block .share-icon{position:absolute; top:50%; right:20px; width:45px; height:45px; line-height:43px; font-size:19px; color:#ffffff; border:1px solid #E7E7E7; -webkit-transition:all .3s ease; transition:all .3s ease; -webkit-transform:translateY(-50%); transform:translateY(-50%); text-align:center; z-index:3;}
.team-block .social-links{position:absolute; right:20px; bottom:100%; margin-bottom:-10px; display:-webkit-box; display:-ms-flexbox; display:flex; -webkit-box-align:center; -ms-flex-align:center; align-items:center; -webkit-box-orient:vertical; -webkit-box-direction:normal; -ms-flex-direction:column; flex-direction:column; -webkit-transform:scaleY(0); transform:scaleY(0); -webkit-transform-origin:bottom; transform-origin:bottom; z-index:3; opacity:0; visibility:hidden; -webkit-transition:all .4s ease; transition:all .4s ease;}
.team-block .social-links a{position:relative; height:46px; width:46px; line-height:46px; text-align:center; background-color:#000000; border:1px solid #ffffff; margin-top:10px; -webkit-transition:all .3s ease; transition:all .3s ease;}
.team-block .social-links a * {color: #fff;}
.team-block .inner-box:hover .image img{-webkit-transform:scale(1.1); transform:scale(1.1); filter:grayscale(0);}
.team-block .inner-box:hover .info-box:before{width:100%;}
.team-block .inner-box:hover .info-box:after{background-color:var(--primary);}
.team-block .inner-box:hover .info-box .name,.team-block .inner-box:hover .info-box .designation{color:#ffffff;}
.team-block .inner-box:hover .info-box .share-icon{background-color:var(--primary);}
.team-block .inner-box:hover .social-links{-webkit-transform:scaleY(1); transform:scaleY(1); opacity:1; visibility:visible;}
.team-block .social-links a:hover{color:#ffffff; background-color:var(--primary);}





/*--------------------------------------------------------------
# Footer
--------------------------------------------------------------*/

.footerAddSec1{color:#ffffff; background:#000000;}
.footerAddSec1Col img{width:200px; filter:invert(100%);}
.footerAddSec1Col ul li a{color:#ffffff; display:inline-block; font-size:14px; line-height:22px; text-transform:uppercase;}
.footerAddSec1Col ul li a br{display:none;}
.footerAddSec1Col ul li a:hover{color:var(--primary);}
.footerAddSec1Col h4{margin:0 0 20px 0; font-size:20px; line-height:22px; font-weight:800; color:#fff;}
.footerAddSec1Col a{color:#ffffff; display:block; font-size:14px; line-height:22px; text-transform:uppercase;}
.footerAddSec1Col p{font-size:15px; line-height:22px; margin-top:20px; font-weight:600;}
.footerAddSec2{background:var(--primary); text-align:center; padding:5px 0;}
.footerAddSec2 p{color:#ffffff; margin:0; font-weight:600;}
.footerAddSec2 p a{color:#ffffff; margin:0; font-weight:600;}
.pt-6{padding-top:5rem !important;}
.pb-6{padding-bottom:5rem !important;}
.py-6{padding-top:5rem !important; padding-bottom:5rem !important;}
.py-7{padding-top:7rem !important; padding-bottom:7rem !important;}
.form-fields,.form-fields2,.form-fields3{width:100% !important; box-sizing:border-box; padding:5px 0; font-size:15px; margin-bottom:15px; background:none; border:none; color:#ffffff; border-bottom:2px solid #ffffff; -webkit-transition:0.3s ease-in-out !important; -moz-transition:0.3s ease-in-out !important; -ms-transition:0.3s ease-in-out !important; -o-transition:0.3s ease-in-out !important; transition:0.3s ease-in-out !important;}
.form-fields:focus,.form-fields2:focus,.form-fields3:focus{border:none; border-bottom:2px solid #ffffff; outline:none !important;}
.form-fields3{height:130px;}
.wpcf7-submit,.ln-widgetBox.search .searchButton a{float:right; position:relative; padding:10px 30px; font-weight:700; display:inline-block; text-transform:uppercase; border:1px solid #ffffff; background:#000; color:#ffffff; margin-top:15px;}
.wpcf7-submit:hover{border:1px solid #000000; background:#000000; color:#ffffff;}
div.wpcf7 img.ajax-loader{float:left;}
.wpcf7-list-item{display:inline-block; margin-right:10px;}
div.wpcf7-response-output{float:left;}
.wpcf7-not-valid-tip{display:none !important;}
.wpcf7-not-valid{border-bottom:2px solid red !important; border:none;}
::placeholder{font-size:14px; text-transform:uppercase; color:#ffffff;}
.wpcf7 form.invalid .wpcf7-response-output,.wpcf7 form.unaccepted .wpcf7-response-output,.wpcf7 form.payment-required .wpcf7-response-output{border-color:#ffb900; color:#ffffff;}
.modal-header h1{text-transform:uppercase; font-weight:700;}
.modal-body .form-fields,.modal-body .form-fields2,.modal-body .form-fields3{border-bottom:1px solid #cccccc; color:#000000; font-weight:500;}
.modal-body::placeholder{color:#000000 !important; font-weight:500; font-size:12px;}
.modal-body .wpcf7 form.invalid .wpcf7-response-output,.modal-body .wpcf7 form.unaccepted .wpcf7-response-output,.modal-body .wpcf7 form.payment-required .wpcf7-response-output{border-color:#ffb900; color:#000000;}
@media (min-width:1400px){
  .container,.container-lg,.container-md,.container-sm,.container-xl,.container-xxl{max-width:1140px;}
}
@media (max-width:991px){
  .homeAddSec4 .homeAddSec4Inner h3{padding:0 20px;}
  .homeAddSec4 .homeAddSec4Inner p{padding:0 20px;}
  .homeAddSec4{padding:2rem 0 2rem 0;}
  .homeAddSec4::before{top:-84px; height:100px;}
  .homeAddSec4 .homeAddSec4Inner ul{flex-wrap:wrap;}
  .homeAddSec4 .homeAddSec4Inner ul li{width:100%;}
  .homeAddSec4 .homeAddSec4Inner ul li:nth-child(2){margin:0;}
  .homeAddSec4 .homeAddSec4Inner ul li{font-size:14px; line-height:18px;}
}
@media (max-width:768px){
  .homeAddSec4 .homeAddSec4Inner h3{font-size:25px;}
  .homeAddSec4{padding:7rem 0 2rem 0;}
  .homeAddSec4::before{top:0;}
}
@media (max-width:481px){
  .contactNo{display:none;}
  .homeAddSec4 .homeAddSec4Inner h3{font-size:20px; line-height:28px;}
}
@media (max-width:320px){
  }
  @media (min-width:1280px) and (max-width:1366px){
  .navbar .dropdown .dropdown ul{left:-90%;}
  .navbar .dropdown .dropdown:hover>ul{left:-100%;}
}