@media (max-width: 1200px) {
    .homeAddSec3Bottom {justify-content: center;}
    .wrapper>div#quote-slider {width: 35%;}
    .wrapper>div#image-slider {width: 65%;}
    .aboutAddSec1 {position: relative; z-index: 0;}
    .aboutIntroSliderBox {position: absolute; top: 0; right: 0; width: 100%; height: 100%; z-index: -1; padding: 0; margin: 0;}
    .aboutIntroSliderBox::before {content:''; position: absolute; top: 0; left: 0; width: 100%; height: 100%; 
        background: rgba(255, 255, 255, 0.9); z-index: 3;
    }
    .aboutAddSec1Bottom {justify-content: center;}
}

@media (max-width: 1024px) {
   .pageMargin {
    margin-top: 106px;
}
 #header.header-scrolled .mobile-nav-show{color: #fff;}
}


@media (max-width: 991px) {
    .homeAddSec3Left .owl-carousel .owl-item img {width: 100%; height: auto; object-fit: cover; object-position: center;
    aspect-ratio: 16/9;}
    .homeAddSec3Right h2 {font-size: 28px; line-height: 1.1;}
    .homeAddSec3Right h2 br {display: none;}
    .homeAddSec3RightInner {margin-left: 0;}
    .homeAddSec5Left {z-index: 0;}
    .homeAddSec5Left:before {content:''; position: absolute; top: 0; left: 0; width: 100%; height: 100%; 
        background: rgba(255, 255, 255, 0.85); z-index: -1;
    }
}

@media (max-width: 767px) {
    .homeAddSec1 .homeAddSec1InnerText h1 {font-size: 36px;}
    .homeAddSec1 .homeAddSec1InnerText p {font-size: 17px;}
    .wrapper>div#quote-slider {width: 45%;}
    .wrapper>div#image-slider {width: 55%;}
    .pSingleLeftSec h1 {font-size: 22px; line-height: 1.1;}
    .cAddSec1 .cAddSec1LeftCol h2 {font-size: 34px; line-height: 1.1;}
    .homeAddSec1 a{    margin-top: 20px;
}

.py-6 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
}


.why-work-with-us .cards {
    grid-template-columns: repeat(1, 1fr);
    gap: 15px;
}
.why-work-with-us , .contactSec{padding: 80px 0; background-attachment: scroll !important;}
.why-work-with-us .section-title , .homeAddSec6 .homeAddSec6Head h2{
    font-size: 34px;
    line-height: 34px;
    padding-bottom: 25px;
    margin-bottom: 40px;
}
.homeAddSec6 .pt-5{padding-top: 1rem !important;}

.contactSecRight h2{ font-size: 34px;
    line-height: 34px;
    margin: 0 0 20px 0;}

.contactSecRight{margin-bottom: 30px;}
.homeAddSec5RightCol{padding-left: 0; }


}

@media (max-width: 576px) {
    .homeAddSec1 .homeAddSec1InnerText h1 {font-size: 26px;}
    .homeAddSec1 .homeAddSec1InnerText p {font-size: 15px;}
    .homeAddSec1 .homeAddSec1InnerText p br {display: none;}
     .cusBtn {
        font-size: 15px;
        padding: 11px 22px;
    }
    .hAddSec4 .wrapper {flex-wrap: wrap;}
    .wrapper>div#quote-slider {width: 100%; height: 220px;}
    .wrapper>div#image-slider {width: 100%;}
    .wrapper>div#quote-slider {padding-right: 20px;}
    .homeAddSec1 .homeAddSec1InnerText{padding-left: 1.5rem !important; padding-right: 1.5rem !important;}
    .homeAddSec3Right p {
    font-size: 16px;
    line-height: 20px;
    margin-bottom: 20px;
}
.why-work-with-us .card {padding: 20px 10px;}
.contactSecRight p{font-size: 15px;}
}

@media (max-width: 480px) {
    .wrapper>div#quote-slider {height: 120px;}
    .homeAddSec1 .homeAddSec1InnerText p{margin-bottom: 20px;}
    .homeAddSec1 h1 br {display: none;}
      .homeAddSec1 .cusBtn {
                font-size: 14px;
        display: block;
        margin-top: 7px;
        margin-right: 0 !important;
    }
        #header .logo img {
        width: 160px;
    }
        .mobile-nav-show {
        font-size: 25px;
    
        padding-right: 10px;
    }
        .pageMargin {
        margin-top: 91px;
    }

    .why-work-with-us , .contactSec{padding: 60px 0;}
    .why-work-with-us .section-title , .homeAddSec6 .homeAddSec6Head h2{
    font-size: 28px;
    line-height: 28px;
    padding-bottom: 20px;
    margin-bottom: 30px;
}
.contactSecRight h2{font-size: 28px; line-height: 28px;}
.contact-box{ padding: 14px 12px; gap: 20px;}
.contact-box .icon{width:50px; height:50px; line-height:40px;}
.contact-box h4 {
    font-size: 17px;
    line-height: 17px;}
    .contact-box .content p {font-size: 15px;line-height: 16px;}

    .footerAddSec1Col h4{font-size:18px; line-height:22px;}
    .footerAddSec2 p{font-size:14px; line-height:20px;}

}