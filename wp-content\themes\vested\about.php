<?php

/**
 * Template Name: About
 */
get_header();
global $post;
$meta      = get_field_objects($post->ID);
$home_meta = get_field_objects(2);
?>

<?php
if (have_posts()) {
	while (have_posts()) : the_post();
		$image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
?>
		<div class="container-fluid mainBanner" style="background: url('<?php echo $image[0]; ?>') center center no-repeat; background-size: cover;">
			<div class="container-xl">
				<div class="row mainBannerText">
					<div class="col-12">
						<h2>About</h2>
						<div class="breadcrumb">
							<a href="#">Home</a>
							<span><i class="fa-solid fa-angles-right mx-2"></i>About</span>
						</div>
					</div>
				</div>
			</div>
		</div>
<?php
	endwhile;
}
?>


<div class="aboutAddSec1 py-6">
	<div class="container-xl">
		<div class="row">
			<div class="col-xl-6 col-12">
				<div class="aboutAddSec1Left">
					<h2>About Vested</h2>
					<p>At Vested Commercial, we are committed to unlocking the true potential of commercial real estate opportunities. Based in Phoenix, AZ, our investor-focused and results-driven approach ensures value creation at every stage of the investment lifecycle.</p>
					<div class="accordion accordion-flush" id="accordionFlushExample">
						<div class="accordion-item">
							<h3 class="accordion-header" id="flush-headingOne">
								<button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
									data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
									Our Journey
								</button>
							</h3>
							<div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
								data-bs-parent="#accordionFlushExample">
								<div class="accordion-body">
									<p>
										Founded with a vision to transform high-potential commercial properties into thriving investments, Vested Commercial has built a reputation for delivering measurable results. Our expertise spans acquisitions, repositioning, and asset management, enabling our clients to maximize returns while minimizing risks.
									</p>
								</div>
							</div>
						</div>

						<div class="accordion-item">
							<h3 class="accordion-header" id="flush-headingTwo">
								<button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
									data-bs-target="#flush-collapseTwo" aria-expanded="false" aria-controls="flush-collapseTwo">
									Our Mission
								</button>
							</h3>
							<div id="flush-collapseTwo" class="accordion-collapse collapse" aria-labelledby="flush-headingTwo"
								data-bs-parent="#accordionFlushExample">
								<div class="accordion-body">
									<p>To create lasting value through strategic real estate investments that benefit our partners, communities, and the marketplace.</p>
								</div>
							</div>
						</div>

						<div class="accordion-item">
							<h3 class="accordion-header" id="flush-headingThree">
								<button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
									data-bs-target="#flush-collapseThree" aria-expanded="false" aria-controls="flush-collapseThree">
									Our Vision
								</button>
							</h3>
							<div id="flush-collapseThree" class="accordion-collapse collapse" aria-labelledby="flush-headingThree"
								data-bs-parent="#accordionFlushExample">
								<div class="accordion-body">
									<p>To be the leading name in value-add commercial real estate in Phoenix, recognized for innovation, integrity, and exceptional results</p>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="col-xl-6 col-12 aboutIntroSliderBox">
				<div class="aboutAddSec1Right owl-carousel owl-theme">
					<div class="aboutAddSec1RightThumb">
						<img src="https://demo.focusedcre.com/vested/wp-content/uploads/2025/03/property-image-1.jpg" alt="">
					</div>
					<div class="aboutAddSec1RightThumb">
						<img src="https://demo.focusedcre.com/vested/wp-content/uploads/2025/03/property-image-2.jpg" alt="">
					</div>
					<div class="aboutAddSec1RightThumb">
						<img src="https://demo.focusedcre.com/vested/wp-content/uploads/2025/03/property-image.jpg" alt="">
					</div>
				</div>
			</div>
		</div>

	</div>
</div>

<section class="aboutAddSec1BottomSec py-6">
	<div class="container-fluid">
		<div class="row px-5">

			<div class="col-12">
				<div class="aboutAddSec1Bottom">
					<div class="aboutAddSec1BottomStats">
						<h2><span>$</span><span class="counter">75</span><span>M</span></h2>
						<p>Total Transactions</p>
					</div>
					<div class="aboutAddSec1BottomStats">
						<h2><span>$</span><span class="counter">42</span><span>M</span></h2>
						<p>Total Assets</p>
					</div>
					<div class="aboutAddSec1BottomStats">
						<h2><span class="counter">30</span><span>+</span></h2>
						<p>Years of Experience</p>
					</div>
					<div class="aboutAddSec1BottomStats">
						<h2><span class="counter">1.5</span><span>M</span></h2>
						<p>Square Feet Managed</p>
					</div>
					<div class="aboutAddSec1BottomStats">
						<h2><span class="counter">27</span></h2>
						<p>Team Members</p>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>

<style>
	.aboutAddSec1BottomSec {
		background-color: #efefee;
	}
</style>

<div class="container-xl py-6 aboutAddSec2">
	<div class="row justify-content-center">
		<?php
		$args = query_posts(
			array(
				'post_type' => 'team', // This is the name of your CPT
				'order' => 'ASC',
				'posts_per_page' => -1
			)
		);
		if (have_posts()) {
			while (have_posts()) : the_post();
				$image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
				$designation = get_field('designation');
				$designation_2 = get_field('designation_2');
				$email = get_field('email');
				$phone = get_field('phone');
				$linkedin = get_field('linkedin');
		?>
				<div class="col-md-4">
					<div class="team-block mb-30">
						<div class="inner-box">
							<div class="image-box">
								<figure class="image">
									<a href="javascript:void(0)" style="cursor: default;"><img src="<?php echo $image[0]; ?>" alt=""></a>
								</figure>
								<div class="info-box">
									<h4><?php the_title(); ?></h4>
									<?php if ($designation) : ?>
										<span class="designation"><?php the_field('designation'); ?></span>
									<?php endif; ?>
									<?php if ($designation_2) : ?>
										<span class="designation"><?php the_field('designation_2'); ?></span>
									<?php endif; ?>

									<span class="share-icon fa fa-share-alt"></span>
									<div class="social-links">
										<?php if ($phone) : ?>
											<a href="tel:<?= $phone ?>"><i class="fa-solid fa-phone-volume"></i></a>
										<?php endif; ?>
										<?php if ($email) : ?>
											<a href="mailto:<?= $email ?>"><i class="fa-solid fa-envelope-open-text"></i></a>
										<?php endif; ?>
										<!-- 										<?php if ($linkedin) : ?>
										<a href="<?= $linkedin ?>" target="_blank"><i class="fa-brands fa-linkedin-in"></i></a>
										<?php endif; ?> -->
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
		<?php
			endwhile;
		}
		wp_reset_query();
		?>
	</div>
</div>


<?php get_footer(); ?>
<script src="https://code.jquery.com/jquery-1.12.4.min.js" integrity="sha256-ZosEbRLbNQzLpnKIkEdrPv7lOy9C27hHQ+Xp8a4MxAQ=" crossorigin="anonymous"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.theme.default.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js"></script>
<script>
	$(document).ready(function() {
		$(".aboutAddSec1Right").owlCarousel({
			loop: true,
			margin: 0,
			nav: false,
			dots: false,
			items: 1,
			autoplay: true,
			autoplayTimeout: 3000,
			navText: [
				'<span class="fa-solid fa-arrow-right-long left"></span>',
				'<span class="fa-solid fa-arrow-right-long"></span>',
			],
			responsive: {
				0: {
					items: 1
				},
				991: {
					items: 1
				}
			},
		});
	});
</script>

<script>
	jQuery(document).ready(function($) {
		$('.counter').counterUp({
			delay: 10, // the delay time in ms
			time: 3000 // the speed time in ms
		});
	});
</script>