<?php

/**
 * Template Name: Home
 */
get_header();
global $post;
$meta      = get_field_objects($post->ID);
$home_meta = get_field_objects(2);
?>

<?php
if (have_posts()) {
	while (have_posts()) : the_post();
		$image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
?>
		<section id="homeSec" class="id-scrool-fix"></section>

		<div class="homeAddSec1 pageMargin">
			<div class="row">
				<div class="col-12 px-0 homeAddSec1Inner">
					<!-- get featured image of the homepage in video poster-->
					<video autoplay muted loop playsinline poster="<?php echo $image[0]; ?>">
						<source src="#" type="video/mp4">
						Your browser does not support the video tag.
					</video>

					<div class="container-xl">
						<div class="row">
							<div class="col-xl-8 col-lg-8 col-md-8 col-12">
								<div class="px-xl-0 px-5 homeAddSec1InnerText">
									<?php echo $home_meta['hero_section_content']['value']; ?>
									<a href="#propertiesSec" class="cusBtn cusBtn1 me-3"><?php echo $home_meta['hero_section_btn_1']['value']; ?></a>
									<a href="#contactSec" class="cusBtn cusBtn2"><?php echo $home_meta['hero_section_btn_2']['value']; ?></a>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
<?php
	endwhile;
}
?>

<section id="aboutSec" class="id-scrool-fix"></section>
<div class="homeAddSec3 py-6">
	<div class="container-xl">
		<div class="row align-items-center gx-5">
			<div class="col-lg-7 col-md-6 col-12">
				<div class="homeAddSec3Left">
					<img src="<?php echo $home_meta['about_us_image']['value']; ?>" class="img-fluid" alt="">
				</div>
			</div>
			<div class="col-lg-5 col-md-6 col-12 mt-lg-0 mt-4">
				<div class="homeAddSec3Right">
					<?php the_content(); ?>
					<a href="#contactSec" class="cusBtn cusBtn3"><?php echo $home_meta['about_us_btn']['value']; ?></a>
				</div>
			</div>
		</div>
	</div>
</div>

<section id="servicesSec" class="id-scrool-fix"></section>


<section class="why-work-with-us text-white" style="background: url('<?php echo $home_meta['our_expertise_background_image']['value']; ?>') center center no-repeat; background-size: cover; background-attachment: fixed;">
	<div class="overlay">
		<div class="container-xl">
			<div class="row align-items-center">
				<div class="col-md-12">
					<div class="text-center">
						<h2 class="section-title"><?php echo $home_meta['our_expertise_heading']['value']; ?></h2>
					</div>
				</div>
				<div class="col-md-12">
					<div class="cards">
						<!-- call acf repeater field our_expertise_boxes for card -->

						<?php
						if (have_rows('our_expertise_boxes', 2)) :
							while (have_rows('our_expertise_boxes', 2)) : the_row();
								$icon = get_sub_field('icon_code');
								$title = get_sub_field('heading');
								$description = get_sub_field('sub_text');
						?>
								<div class="card">
									<div class="icon"><?php echo $icon; ?></div>
									<h3><?php echo $title; ?></h3>
									<p><?php echo $description; ?></p>
								</div>
						<?php
							endwhile;
						endif;
						?>
					</div>
				</div>
			</div>

		</div>
	</div>
</section>

<section id="propertiesSec" class="id-scrool-fix"></section>
<section class="homeAddSec6 py-6 d-flex align-items-center">
	<div class="container-xl">
		<div class="row">
			<div class="col-lg-12">
				<div class="homeAddSec6Head">
					<h2><?php echo $home_meta['recent_investment_opportunities_heading']['value']; ?></h2>
				</div>
			</div>
		</div>
		<div class="row pt-5">
			<div class="col-12">
				<div class="homeAddSec6Slide owl-carousel">
					<?php
					$args = query_posts(
						array(
							'post_type' => 'properties', // This is the name of your CPT
							'order' => 'DSC',
							'posts_per_page' => -1
						)
					);
					if (have_posts()) {
						while (have_posts()) : the_post();
							$image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
							$citystate = get_field("address");
					?>
							<div class="homeAddSec6Col">
								<img src="<?php echo $image[0]; ?>" alt="">
								<h4><?php the_title(); ?></h4>
								<p><?php echo $citystate; ?></p>
							</div>
					<?php
						endwhile;
					}
					wp_reset_query();
					?>
				</div>
			</div>
		</div>
	</div>
</section>


<section id="contactSec" class="id-scrool-fix{"></section>
<div class="contactSec" style="background: url('<?php echo $home_meta['contact_us_background_image']['value']; ?>') center center no-repeat; background-size:cover; background-attachment: fixed;">
	<div class="container-xl">
		<div class="row align-items-center">
			<div class="col-md-6 col-12">
				<div class="contactSecRight">
					<h2><?php echo $home_meta['get_in_touch_heading']['value']; ?></h2>
					<p><?php echo $home_meta['get_in_touch_sub_text']['value']; ?></p>
					<div class="mt-4">
						<?php echo do_shortcode('[contact-form-7 id="6378e04" title="Contact form 1"]'); ?>
					</div>
				</div>
			</div>
			<div class="col-md-6 col-12">
				<div class="contactSecLeft">

					<div class="homeAddSec5RightCol">
						<div class="contact-box">
							<div class="icon">
								<i class="fa fa-envelope"></i>
							</div>
							<div class="content">
								<h4>Email</h4>
								<p><a href="mailto:<?php echo $home_meta['email']['value']; ?>"><?php echo $home_meta['email']['value']; ?></a></p>
							</div>
						</div>
						<div class="contact-box">
							<div class="icon">
								<i class="fa-solid fa-phone-volume"></i>
							</div>
							<div class="content">
								<h4>Phone</h4>
								<p><a href="tel:<?php echo $home_meta['phone']['value']; ?>"><?php echo $home_meta['phone']['value']; ?></a></p>
							</div>
						</div>

						<div class="contact-box">
							<div class="icon">
								<i class="fa-solid fa-location-dot"></i>
							</div>
							<div class="content">
								<h4>Address</h4>
								<p><?php echo $home_meta['address']['value']; ?></p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<?php get_footer(); ?>

<script src="https://code.jquery.com/jquery-1.12.4.min.js" integrity="sha256-ZosEbRLbNQzLpnKIkEdrPv7lOy9C27hHQ+Xp8a4MxAQ=" crossorigin="anonymous"></script>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.theme.default.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js"></script>



<script>
	$(document).ready(function() {
		$(".homeAddSec6Slide").owlCarousel({
			loop: true,
			nav: true,
			navText: [
				'<i class="fa fa-angle-left"></i>',
				'<i class="fa fa-angle-right"></i>',
			],
			autoplay: true,
			autoplayTimeout: 4000,
			responsiveClass: true,
			margin: 30,
			autoplayHoverPause: true,
			dots: false,
			responsive: {
				0: {
					items: 1,
				},
				576: {
					items: 2,
				},
				1366: {
					items: 3,
				},
			},
		});
	});
</script>