/*---------------------------------------------------------------------------------------------
*
*  Repeater
*
*---------------------------------------------------------------------------------------------*/
.acf-repeater {
  /* table */
  /* row handle (add/remove) */
  /* add in spacer to th (force correct width) */
  /* row */
  /* sortable */
  /* layouts */
  /*
	&.-row > table > tbody > tr:before,
	&.-block > table > tbody > tr:before {
		content: "";
		display: table-row;
		height: 2px;
		background: #f00;
	}
*/
  /* empty */
  /* collapsed */
  /* collapsed (block layout) */
  /* collapsed (table layout) */
}
.acf-repeater > table {
  margin: 0 0 8px;
  background: #F9F9F9;
}
.acf-repeater .acf-row-handle {
  width: 16px;
  text-align: center !important;
  vertical-align: middle !important;
  position: relative;
  /* icons */
  /* .order */
  /* remove */
}
.acf-repeater .acf-row-handle .acf-icon {
  display: none;
  position: absolute;
  top: 0;
  margin: -8px 0 0 -2px;
  /* minus icon */
}
.acf-repeater .acf-row-handle .acf-icon.-minus {
  top: 50%;
  /* ie fix */
}
body.browser-msie .acf-repeater .acf-row-handle .acf-icon.-minus {
  top: 25px;
}
.acf-repeater .acf-row-handle.order {
  background: #f4f4f4;
  cursor: move;
  color: #aaa;
  text-shadow: #fff 0 1px 0;
}
.acf-repeater .acf-row-handle.order:hover {
  color: #666;
}
.acf-repeater .acf-row-handle.order + td {
  border-left-color: #DFDFDF;
}
.acf-repeater .acf-row-handle.remove {
  background: #F9F9F9;
  border-left-color: #DFDFDF;
}
.acf-repeater th.acf-row-handle:before {
  content: "";
  width: 16px;
  display: block;
  height: 1px;
}
.acf-repeater .acf-row {
  /* hide clone */
  /* hover */
}
.acf-repeater .acf-row.acf-clone {
  display: none !important;
}
.acf-repeater .acf-row:hover,
.acf-repeater .acf-row.-hover {
  /* icons */
}
.acf-repeater .acf-row:hover > .acf-row-handle .acf-icon,
.acf-repeater .acf-row.-hover > .acf-row-handle .acf-icon {
  display: block;
}
.acf-repeater > table > tbody > tr.ui-sortable-helper {
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
}
.acf-repeater > table > tbody > tr.ui-sortable-placeholder {
  visibility: visible !important;
}
.acf-repeater > table > tbody > tr.ui-sortable-placeholder td {
  background: #F9F9F9;
}
.acf-repeater.-row > table > tbody > tr > td,
.acf-repeater.-block > table > tbody > tr > td {
  border-top-color: #E1E1E1;
}
.acf-repeater.-empty > table {
  border-bottom: 0 none;
}
.acf-repeater.-empty.-row > table,
.acf-repeater.-empty.-block > table {
  display: none;
}
.acf-repeater .acf-row.-collapsed > .acf-field {
  display: none !important;
}
.acf-repeater .acf-row.-collapsed > td.acf-field.-collapsed-target {
  display: table-cell !important;
}
.acf-repeater .acf-row.-collapsed > .acf-fields > * {
  display: none !important;
}
.acf-repeater .acf-row.-collapsed > .acf-fields > .acf-field.-collapsed-target {
  display: block !important;
}
.acf-repeater.-table .acf-row.-collapsed .acf-field.-collapsed-target {
  border-left-color: #dfdfdf;
}