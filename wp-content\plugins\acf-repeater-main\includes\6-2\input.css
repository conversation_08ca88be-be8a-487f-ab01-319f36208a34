
/*---------------------------------------------------------------------------------------------
*
*  Repeater
*
*---------------------------------------------------------------------------------------------*/
.acf-repeater {
    /* table */
    /* row handle (add/remove) */
    /* add in spacer to th (force correct width) */
    /* row */
    /* sortable */
    /* layouts */
    /*
        &.-row > table > tbody > tr:before,
        &.-block > table > tbody > tr:before {
            content: "";
            display: table-row;
            height: 2px;
            background: #f00;
        }
    */
    /* empty */
    /* collapsed */
    /* collapsed (block layout) */
    /* collapsed (table layout) */
  }
  .acf-repeater > table {
    margin: 0 0 8px;
    background: #F9F9F9;
  }
  .acf-repeater > table > tbody tr.acf-divider:not(:first-child) > td {
    border-top: 10px solid #EAECF0;
  }
  .acf-repeater .acf-row-handle {
    width: 16px;
    text-align: center !important;
    vertical-align: middle !important;
    position: relative;
    /* icons */
    /* .order */
    /* remove */
  }
  .acf-repeater .acf-row-handle .acf-order-input-wrap {
    width: 45px;
  }
  .acf-repeater .acf-row-handle .acf-order-input::-webkit-outer-spin-button,
  .acf-repeater .acf-row-handle .acf-order-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  .acf-repeater .acf-row-handle .acf-order-input {
    -moz-appearance: textfield;
    text-align: center;
  }
  .acf-repeater .acf-row-handle .acf-icon {
    display: none;
    position: absolute;
    top: 0;
    margin: -8px 0 0 -2px;
    /* minus icon */
  }
  .acf-repeater .acf-row-handle .acf-icon.-minus {
    top: 50%;
    /* ie fix */
  }
  body.browser-msie .acf-repeater .acf-row-handle .acf-icon.-minus {
    top: 25px;
  }
  .acf-repeater .acf-row-handle.order {
    background: #f4f4f4;
    cursor: move;
    color: #aaa;
    text-shadow: #fff 0 1px 0;
  }
  .acf-repeater .acf-row-handle.order:hover {
    color: #666;
  }
  .acf-repeater .acf-row-handle.order + td {
    border-left-color: #DFDFDF;
  }
  .acf-repeater .acf-row-handle.pagination {
    cursor: auto;
  }
  .acf-repeater .acf-row-handle.remove {
    background: #F9F9F9;
    border-left-color: #DFDFDF;
  }
  .acf-repeater th.acf-row-handle:before {
    content: "";
    width: 16px;
    display: block;
    height: 1px;
  }
  .acf-repeater .acf-row {
    /* hide clone */
    /* hover */
  }
  .acf-repeater .acf-row.acf-clone {
    display: none !important;
  }
  .acf-repeater .acf-row:hover, .acf-repeater .acf-row.-hover {
    /* icons */
  }
  .acf-repeater .acf-row:hover > .acf-row-handle .acf-icon, .acf-repeater .acf-row.-hover > .acf-row-handle .acf-icon {
    display: block;
  }
  .acf-repeater .acf-row:hover > .acf-row-handle .acf-icon.show-on-shift, .acf-repeater .acf-row.-hover > .acf-row-handle .acf-icon.show-on-shift {
    display: none;
  }
  body.acf-keydown-shift .acf-repeater .acf-row:hover > .acf-row-handle .acf-icon.show-on-shift, body.acf-keydown-shift .acf-repeater .acf-row.-hover > .acf-row-handle .acf-icon.show-on-shift {
    display: block;
  }
  body.acf-keydown-shift .acf-repeater .acf-row:hover > .acf-row-handle .acf-icon.hide-on-shift, body.acf-keydown-shift .acf-repeater .acf-row.-hover > .acf-row-handle .acf-icon.hide-on-shift {
    display: none;
  }
  .acf-repeater > table > tbody > tr.ui-sortable-helper {
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
  }
  .acf-repeater > table > tbody > tr.ui-sortable-placeholder {
    visibility: visible !important;
  }
  .acf-repeater > table > tbody > tr.ui-sortable-placeholder td {
    background: #F9F9F9;
  }
  .acf-repeater.-row > table > tbody > tr > td, .acf-repeater.-block > table > tbody > tr > td {
    border-top-color: #E1E1E1;
  }
  .acf-repeater.-empty > table > thead > tr > th {
    border-bottom: 0 none;
  }
  .acf-repeater.-empty.-row > table, .acf-repeater.-empty.-block > table {
    display: none;
  }
  .acf-repeater .acf-row.-collapsed > .acf-field {
    display: none !important;
  }
  .acf-repeater .acf-row.-collapsed > td.acf-field.-collapsed-target {
    display: table-cell !important;
  }
  .acf-repeater .acf-row.-collapsed > .acf-fields > * {
    display: none !important;
  }
  .acf-repeater .acf-row.-collapsed > .acf-fields > .acf-field.-collapsed-target {
    display: block !important;
  }
  .acf-repeater .acf-row.-collapsed > .acf-fields > .acf-field.-collapsed-target[data-width] {
    float: none !important;
    width: auto !important;
  }
  .acf-repeater.-table .acf-row.-collapsed .acf-field.-collapsed-target {
    border-left-color: #dfdfdf;
  }
  .acf-repeater.-max .acf-icon[data-event=add-row] {
    display: none !important;
  }
  .acf-repeater > .acf-actions .acf-button {
    float: right;
    pointer-events: auto !important;
  }
  .acf-repeater > .acf-actions .acf-tablenav {
    float: right;
    margin-right: 20px;
  }
  .acf-repeater > .acf-actions .acf-tablenav .current-page {
    width: auto !important;
  }